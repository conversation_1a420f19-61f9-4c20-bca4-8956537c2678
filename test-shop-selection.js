/**
 * 测试闲鱼店铺选择功能
 */

const GoofishProductPublisher = require('./goofish-product-publisher.js');

async function testShopSelection() {
    const publisher = new GoofishProductPublisher();

    try {
        console.log('🧪 开始测试店铺选择功能...');

        // 初始化浏览器
        await publisher.init({
            usePersistentContext: true,
            contextOptions: {
                headless: false,
                slowMo: 500
            }
        });

        // 打开商品发布页面
        await publisher.openProductAddPage();

        // 先选择普通商品类型（这是必须的步骤）
        console.log('📋 选择普通商品类型...');
        await publisher.page.getByRole('radio', { name: /普通商品/ }).click();
        await publisher.page.waitForTimeout(2000);

        // 等待页面加载完成
        console.log('⏳ 等待页面加载...');
        await publisher.page.waitForTimeout(3000);

        // 测试店铺选择
        console.log('\n🏪 测试店铺选择功能...');
        const shopSelected = await publisher.selectXianyuShop();

        if (shopSelected) {
            console.log('🎉 店铺选择测试成功！');

            // 验证选择结果
            console.log('\n🔍 验证选择结果...');
            const serviceSection = await publisher.page.$('text=服务保障');
            const productInfoSection = await publisher.page.$('text=商品信息');

            if (serviceSection && productInfoSection) {
                console.log('✅ 验证成功：页面已展开所有相关内容');
                console.log('  - 服务保障区域：已显示');
                console.log('  - 商品信息区域：已显示');
            } else {
                console.log('⚠️ 验证警告：部分内容可能未正确展开');
            }

        } else {
            console.log('❌ 店铺选择测试失败！');
        }

        // 调试页面状态
        console.log('\n🔍 调试页面状态...');
        await publisher.debugPageStatus();

        // 保持浏览器打开以便手动检查
        console.log('\n🔍 浏览器将保持打开状态，请手动检查店铺选择结果...');
        console.log('📝 检查要点：');
        console.log('  1. 店铺是否已选中（有选中标识）');
        console.log('  2. 服务保障区域是否显示');
        console.log('  3. 商品信息区域是否显示');
        console.log('  4. 价格库存区域是否可编辑');
        console.log('\n按 Ctrl+C 退出测试');

        // 等待用户手动检查
        await new Promise(resolve => {
            process.on('SIGINT', () => {
                console.log('\n👋 测试结束');
                resolve();
            });
        });

    } catch (error) {
        console.error('❌ 测试过程中出现错误：', error);

        // 保存错误截图
        try {
            await publisher.page.screenshot({ path: 'test-error.png' });
            console.log('📸 已保存错误截图：test-error.png');
        } catch (screenshotError) {
            console.warn('⚠️ 无法保存错误截图');
        }

    } finally {
        // await publisher.close();
    }
}

// 运行测试
if (require.main === module) {
    testShopSelection().catch(console.error);
}

module.exports = testShopSelection;
