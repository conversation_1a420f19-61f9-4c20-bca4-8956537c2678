/**
 * 简化的店铺选择测试
 */

const { chromium } = require('playwright');

async function testShopSelectionSimple() {
    console.log('🧪 开始简化的店铺选择测试...');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 500
    });
    
    const page = await browser.newPage();
    
    try {
        // 1. 导航到页面
        console.log('🌐 导航到商品发布页面...');
        await page.goto('https://www.goofish.pro/sale/product/add');
        await page.waitForTimeout(3000);
        
        // 2. 选择普通商品类型
        console.log('📋 选择普通商品类型...');
        await page.getByRole('radio', { name: /普通商品/ }).click();
        await page.waitForTimeout(2000);
        
        // 3. 等待店铺选择区域加载
        console.log('⏳ 等待店铺选择区域加载...');
        await page.waitForSelector('text=发布的闲鱼店铺', { timeout: 5000 });
        
        // 4. 测试店铺选择
        console.log('🏪 开始选择店铺...');
        
        // 方法1：使用Playwright的getByRole选择器
        try {
            console.log('🔍 方法1：使用role选择器...');
            const shopItems = await page.getByRole('listitem').all();
            console.log(`找到 ${shopItems.length} 个列表项`);
            
            for (let i = 0; i < shopItems.length; i++) {
                const item = shopItems[i];
                const text = await item.textContent();
                
                // 检查是否有图片且不是创建选项
                const hasImage = await item.locator('img').count() > 0;
                const isCreateOption = text && text.includes('创建闲鱼店铺');
                
                console.log(`  项目 ${i}: "${text?.trim().substring(0, 30)}..." 有图片:${hasImage} 是创建选项:${isCreateOption}`);
                
                if (hasImage && !isCreateOption && text && text.trim().length > 5) {
                    console.log(`✅ 选择店铺: ${text.trim().substring(0, 30)}...`);
                    await item.click();
                    await page.waitForTimeout(2000);
                    
                    // 验证选择是否成功
                    const serviceSection = await page.locator('text=服务保障').count();
                    if (serviceSection > 0) {
                        console.log('🎉 店铺选择成功！页面已展开相关内容');
                        break;
                    } else {
                        console.log('⚠️ 选择后页面未展开，继续尝试...');
                    }
                }
            }
        } catch (error) {
            console.log('❌ 方法1失败:', error.message);
        }
        
        // 5. 验证最终结果
        console.log('\n🔍 验证最终结果...');
        const serviceSection = await page.locator('text=服务保障').count();
        const productInfoSection = await page.locator('text=商品信息').count();
        
        if (serviceSection > 0 && productInfoSection > 0) {
            console.log('✅ 测试成功！店铺已选择，页面内容已展开');
        } else {
            console.log('❌ 测试失败！店铺可能未正确选择');
        }
        
        // 6. 保存截图
        await page.screenshot({ path: 'shop-selection-result.png' });
        console.log('📸 已保存结果截图：shop-selection-result.png');
        
        // 7. 保持浏览器打开
        console.log('\n🔍 浏览器将保持打开状态，请手动检查结果...');
        console.log('按 Ctrl+C 退出测试');
        
        await new Promise(resolve => {
            process.on('SIGINT', () => {
                console.log('\n👋 测试结束');
                resolve();
            });
        });
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误：', error);
        
        // 保存错误截图
        try {
            await page.screenshot({ path: 'test-simple-error.png' });
            console.log('📸 已保存错误截图：test-simple-error.png');
        } catch (screenshotError) {
            console.warn('⚠️ 无法保存错误截图');
        }
        
    } finally {
        // await browser.close();
    }
}

// 运行测试
if (require.main === module) {
    testShopSelectionSimple().catch(console.error);
}

module.exports = testShopSelectionSimple;
