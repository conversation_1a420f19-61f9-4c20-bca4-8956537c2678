const { chromium } = require('playwright');
const path = require('path');
const fs = require('fs');

async function screenshotGoofish() {
    let browser;
    
    try {
        console.log('启动浏览器...');
        browser = await chromium.launch({
            headless: false, // 设置为 true 可以无头模式运行
            slowMo: 1000    // 减慢操作速度，便于观察
        });
        
        const context = await browser.newContext({
            viewport: { width: 1920, height: 1080 }
        });
        
        const page = await context.newPage();

        // 监控网络请求
        const apiRequests = [];
        page.on('response', response => {
            if (response.url().includes('api.goofish.pro')) {
                apiRequests.push({
                    url: response.url(),
                    status: response.status(),
                    method: response.request().method()
                });
                console.log(`API请求: ${response.request().method()} ${response.url()} - ${response.status()}`);
            }
        });

        console.log('正在访问 goofish.pro 登录页面...');
        await page.goto('https://www.goofish.pro/login', {
            waitUntil: 'networkidle',
            timeout: 30000
        });

        console.log('等待登录页面加载...');
        // 等待页面完全加载
        await page.waitForTimeout(2000);

        // 等待二维码API请求完成
        console.log('等待二维码生成...');
        let qrCodeFound = false;

        // 尝试多种二维码选择器
        const qrSelectors = [
            'img[alt="Scan me!"]',
            'img[src*="qr"]',
            'img[src*="code"]',
            'canvas',
            '[class*="qr"] img',
            '[class*="code"] img'
        ];

        for (const selector of qrSelectors) {
            try {
                await page.waitForSelector(selector, { timeout: 3000 });
                console.log(`找到二维码元素: ${selector}`);
                qrCodeFound = true;
                break;
            } catch (e) {
                // 继续尝试下一个选择器
            }
        }

        if (!qrCodeFound) {
            console.log('未找到二维码元素，等待更长时间...');
            await page.waitForTimeout(5000);
        } else {
            // 额外等待确保二维码完全加载
            await page.waitForTimeout(2000);
        }
        
        // 创建screenshots目录（如果不存在）
        const screenshotDir = path.join(__dirname, 'screenshots');
        if (!fs.existsSync(screenshotDir)) {
            fs.mkdirSync(screenshotDir, { recursive: true });
        }
        
        // 生成文件名（包含时间戳）
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `goofish-pro-${timestamp}.png`;
        const filepath = path.join(screenshotDir, filename);
        
        console.log('正在截图...');
        await page.screenshot({
            path: filepath,
            fullPage: true,
            type: 'png'
        });
        
        console.log(`截图已保存到: ${filepath}`);
        
        // 获取页面标题和URL
        const title = await page.title();
        const url = page.url();

        console.log(`页面标题: ${title}`);
        console.log(`页面URL: ${url}`);

        // 检查页面内容
        let hasQRCode = false;
        for (const selector of qrSelectors) {
            if (await page.locator(selector).count() > 0) {
                hasQRCode = true;
                console.log(`找到二维码 (${selector}): true`);
                break;
            }
        }

        const hasLoginForm = await page.locator('text=闲管家账号登录').count() > 0;

        console.log(`是否找到二维码: ${hasQRCode}`);
        console.log(`是否找到登录表单: ${hasLoginForm}`);

        // 如果没有找到二维码，尝试查找其他登录元素
        if (!hasQRCode) {
            const loginElements = await page.locator('[class*="login"], [class*="qr"], [class*="code"]').count();
            console.log(`找到的登录相关元素数量: ${loginElements}`);
        }

        // 显示API请求总结
        console.log(`\n=== API请求总结 ===`);
        console.log(`总共捕获到 ${apiRequests.length} 个API请求:`);
        apiRequests.forEach((req, index) => {
            console.log(`${index + 1}. ${req.method} ${req.url} - ${req.status}`);
        });
        
    } catch (error) {
        console.error('发生错误:', error.message);
    } finally {
        if (browser) {
            await browser.close();
            console.log('浏览器已关闭');
        }
    }
}

// 运行脚本
if (require.main === module) {
    screenshotGoofish().catch(console.error);
}

module.exports = { screenshotGoofish };
