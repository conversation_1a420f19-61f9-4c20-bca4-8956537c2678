# Goofish.pro 截图脚本

这个Node.js脚本可以自动打开goofish.pro网站并截图保存。

## 安装依赖

如果还没有安装playwright，请运行：

```bash
npm install
npm run install-playwright
```

## 使用方法

### 方法1：使用npm脚本
```bash
npm run screenshot
```

### 方法2：直接运行
```bash
node screenshot-goofish.js
```

## 功能特点

- 自动打开goofish.pro网站
- 等待页面完全加载
- 截取完整页面截图
- 自动创建screenshots目录
- 文件名包含时间戳，避免覆盖
- 显示页面标题和URL信息
- 错误处理和资源清理

## 输出

截图将保存在 `screenshots/` 目录下，文件名格式为：
```
goofish-pro-YYYY-MM-DDTHH-MM-SS-sssZ.png
```

## 配置选项

可以在脚本中修改以下配置：

- `headless: false` - 设置为true可以无头模式运行
- `slowMo: 1000` - 调整操作速度
- `viewport` - 调整浏览器窗口大小
- `timeout` - 调整页面加载超时时间

## 示例输出

```
启动浏览器...
正在访问 goofish.pro...
正在截图...
截图已保存到: C:\Users\<USER>\Desktop\xian_yu\screenshots\goofish-pro-2025-08-11T09-55-17-729Z.png
页面标题: 闲管家-闲鱼电脑版工作台，轻松管理多闲鱼号
页面URL: https://goofish.pro/login
浏览器已关闭
```
