/**
 * 调试闲鱼API请求脚本
 * 专门用于监控和分析API请求
 */

const { chromium } = require('playwright');
const path = require('path');

async function debugGoofishAPI() {
    let browser = null;
    let context = null;
    let page = null;

    try {
        console.log('🚀 启动浏览器调试模式...');
        
        // 使用与原脚本相同的用户数据目录
        const userDataDir = path.join("E:/qianduan/diandian/", "user-data-dir2");
        
        context = await chromium.launchPersistentContext(userDataDir, {
            headless: false,
            slowMo: 1000,
            viewport: { width: 1920, height: 1080 }
        });
        
        browser = context.browser();
        page = context.pages()[0] || await context.newPage();

        // 监控所有网络请求
        const apiRequests = [];
        const failedRequests = [];

        page.on('request', request => {
            if (request.url().includes('api.goofish.pro')) {
                console.log(`📤 API请求: ${request.method()} ${request.url()}`);
                console.log(`📋 请求头:`, request.headers());
                
                apiRequests.push({
                    method: request.method(),
                    url: request.url(),
                    headers: request.headers(),
                    timestamp: new Date().toISOString()
                });
            }
        });

        page.on('response', async response => {
            if (response.url().includes('api.goofish.pro')) {
                const status = response.status();
                const url = response.url();
                
                console.log(`📥 API响应: ${response.request().method()} ${url} - ${status}`);
                
                try {
                    const responseText = await response.text();
                    console.log(`📄 响应内容:`, responseText);
                    
                    if (status !== 200) {
                        failedRequests.push({
                            url,
                            status,
                            response: responseText,
                            timestamp: new Date().toISOString()
                        });
                    }
                } catch (error) {
                    console.log(`❌ 无法读取响应内容:`, error.message);
                }
            }
        });

        page.on('requestfailed', request => {
            if (request.url().includes('api.goofish.pro')) {
                console.log(`❌ 请求失败: ${request.method()} ${request.url()}`);
                console.log(`❌ 失败原因:`, request.failure());
            }
        });

        console.log('🌐 正在访问登录页面...');
        await page.goto('https://www.goofish.pro/login', {
            waitUntil: 'networkidle',
            timeout: 30000
        });

        console.log('⏳ 等待API请求完成...');
        await page.waitForTimeout(10000); // 等待10秒观察API请求

        // 输出总结
        console.log('\n📊 API请求总结:');
        console.log(`✅ 成功请求数量: ${apiRequests.length}`);
        console.log(`❌ 失败请求数量: ${failedRequests.length}`);

        if (failedRequests.length > 0) {
            console.log('\n❌ 失败的请求详情:');
            failedRequests.forEach((req, index) => {
                console.log(`${index + 1}. ${req.url}`);
                console.log(`   状态码: ${req.status}`);
                console.log(`   响应: ${req.response}`);
                console.log(`   时间: ${req.timestamp}`);
            });
        }

        // 检查页面是否有错误信息
        console.log('\n🔍 检查页面错误信息...');
        const errorElements = await page.$$('[class*="error"], [class*="fail"], .error-message, .fail-message');
        if (errorElements.length > 0) {
            console.log('发现页面错误元素:');
            for (const element of errorElements) {
                const text = await element.textContent();
                console.log(`- ${text}`);
            }
        }

        // 检查控制台错误
        console.log('\n📝 页面控制台信息:');
        page.on('console', msg => {
            if (msg.type() === 'error') {
                console.log(`❌ 控制台错误: ${msg.text()}`);
            } else if (msg.type() === 'warning') {
                console.log(`⚠️ 控制台警告: ${msg.text()}`);
            }
        });

        console.log('\n✅ 调试完成，浏览器将保持打开状态供进一步检查...');
        
        // 保持浏览器打开
        await page.waitForTimeout(60000); // 等待1分钟

    } catch (error) {
        console.error('❌ 调试过程中出现错误:', error);
    } finally {
        // 不关闭浏览器，方便手动检查
        console.log('🔍 浏览器保持打开状态，请手动检查...');
    }
}

// 运行调试
if (require.main === module) {
    debugGoofishAPI().catch(console.error);
}

module.exports = debugGoofishAPI;
