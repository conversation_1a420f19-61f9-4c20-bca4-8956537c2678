/**
 * 浏览器配置示例
 * 展示不同的浏览器启动配置方式
 */

const GoofishProductPublisher = require('./goofish-product-publisher');
const path = require('path');

// 示例商品数据
const sampleProduct = {
    title: '苹果MacBook Pro 2023 14寸 16GB 几乎全新',
    description: '苹果MacBook Pro 2023款 14寸笔记本电脑，配置16GB内存，几乎全新成色。',
    price: 12800,
    stock: 1
};

const imagePath = path.join(__dirname, 'apple.jpg');

/**
 * 配置1：使用MCP Playwright相同的用户数据目录（推荐）
 * 优点：保持登录状态，与MCP共享cookies
 */
async function useSharedUserData() {
    console.log('📁 使用共享用户数据目录配置...');
    
    const publisher = new GoofishProductPublisher();
    const options = {
        usePersistentContext: true, // 启用持久化上下文
        userDataDir: undefined, // 使用默认MCP目录
        contextOptions: {
            headless: false,
            slowMo: 1000
        }
    };
    
    await publisher.publishProductFlow(sampleProduct, imagePath, options);
}

/**
 * 配置2：使用自定义用户数据目录
 * 优点：独立的浏览器配置文件，不影响其他应用
 */
async function useCustomUserData() {
    console.log('🗂️ 使用自定义用户数据目录配置...');
    
    const publisher = new GoofishProductPublisher();
    const customUserDataDir = path.join(__dirname, 'custom-browser-profile');
    
    const options = {
        usePersistentContext: true,
        userDataDir: customUserDataDir,
        contextOptions: {
            headless: false,
            slowMo: 800,
            viewport: { width: 1366, height: 768 }
        }
    };
    
    await publisher.publishProductFlow(sampleProduct, imagePath, options);
}

/**
 * 配置3：使用临时会话（不保存数据）
 * 优点：每次都是全新环境，适合测试
 */
async function useTemporarySession() {
    console.log('🔄 使用临时会话配置...');
    
    const publisher = new GoofishProductPublisher();
    const options = {
        usePersistentContext: false, // 禁用持久化上下文
        contextOptions: {
            headless: false,
            slowMo: 1200
        }
    };
    
    await publisher.publishProductFlow(sampleProduct, imagePath, options);
}

/**
 * 配置4：无头模式（后台运行）
 * 优点：不显示浏览器界面，适合服务器环境
 */
async function useHeadlessMode() {
    console.log('👻 使用无头模式配置...');
    
    const publisher = new GoofishProductPublisher();
    const options = {
        usePersistentContext: true,
        userDataDir: undefined,
        contextOptions: {
            headless: true, // 无头模式
            slowMo: 500 // 更快的执行速度
        }
    };
    
    await publisher.publishProductFlow(sampleProduct, imagePath, options);
}

/**
 * 配置5：使用代理服务器
 * 优点：通过代理访问，适合网络受限环境
 */
async function useProxyServer() {
    console.log('🌐 使用代理服务器配置...');
    
    const publisher = new GoofishProductPublisher();
    const options = {
        usePersistentContext: true,
        userDataDir: undefined,
        contextOptions: {
            headless: false,
            slowMo: 1000,
            proxy: {
                server: 'http://proxy-server:8080',
                // username: 'proxy-user',
                // password: 'proxy-pass'
            }
        }
    };
    
    await publisher.publishProductFlow(sampleProduct, imagePath, options);
}

/**
 * 配置6：移动设备模拟
 * 优点：模拟移动设备访问
 */
async function useMobileEmulation() {
    console.log('📱 使用移动设备模拟配置...');
    
    const publisher = new GoofishProductPublisher();
    const options = {
        usePersistentContext: true,
        userDataDir: undefined,
        contextOptions: {
            headless: false,
            slowMo: 1500,
            viewport: { width: 375, height: 667 },
            userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
            isMobile: true,
            hasTouch: true
        }
    };
    
    await publisher.publishProductFlow(sampleProduct, imagePath, options);
}

// 根据命令行参数选择配置
async function main() {
    const configType = process.argv[2] || 'shared';
    
    console.log(`🚀 启动配置类型: ${configType}\n`);
    
    try {
        switch (configType) {
            case 'shared':
                await useSharedUserData();
                break;
            case 'custom':
                await useCustomUserData();
                break;
            case 'temp':
                await useTemporarySession();
                break;
            case 'headless':
                await useHeadlessMode();
                break;
            case 'proxy':
                await useProxyServer();
                break;
            case 'mobile':
                await useMobileEmulation();
                break;
            default:
                console.log('❌ 未知的配置类型，使用默认配置');
                await useSharedUserData();
        }
    } catch (error) {
        console.error('❌ 执行失败：', error);
    }
}

// 显示使用帮助
function showHelp() {
    console.log(`
🔧 浏览器配置示例使用方法：

node browser-config-examples.js [配置类型]

可用的配置类型：
  shared   - 使用MCP Playwright共享用户数据目录（默认）
  custom   - 使用自定义用户数据目录
  temp     - 使用临时会话（不保存数据）
  headless - 无头模式运行
  proxy    - 使用代理服务器
  mobile   - 移动设备模拟

示例：
  node browser-config-examples.js shared
  node browser-config-examples.js headless
  node browser-config-examples.js mobile
`);
}

if (process.argv.includes('--help') || process.argv.includes('-h')) {
    showHelp();
} else if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    useSharedUserData,
    useCustomUserData,
    useTemporarySession,
    useHeadlessMode,
    useProxyServer,
    useMobileEmulation
};
